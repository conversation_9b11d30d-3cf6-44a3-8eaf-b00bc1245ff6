#!/usr/bin/env python3
"""
视频转音频工具 - 专为 IndexTTS 项目优化

这个工具可以将视频文件转换为 IndexTTS 项目需要的音频格式：
- 采样率：22050 Hz
- 声道：单声道
- 格式：WAV
- 音频值范围：[-1, 1]

支持功能：
- 单个文件转换
- 批量转换
- 时间段截取
- 音量标准化
- 静音检测和去除
- 音频质量检查
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Optional, Tuple
import ffmpeg
import librosa
import soundfile as sf
import numpy as np
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# IndexTTS 音频参数
TARGET_SAMPLE_RATE = 22050
TARGET_CHANNELS = 1  # 单声道
OUTPUT_FORMAT = 'wav'

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = {
    '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', 
    '.m4v', '.3gp', '.ogv', '.ts', '.mts', '.m2ts'
}

# 支持的音频格式（用于检查输入是否已经是音频）
SUPPORTED_AUDIO_FORMATS = {
    '.wav', '.mp3', '.flac', '.aac', '.ogg', '.m4a', '.wma'
}


class VideoToAudioConverter:
    """视频转音频转换器"""
    
    def __init__(self, 
                 sample_rate: int = TARGET_SAMPLE_RATE,
                 channels: int = TARGET_CHANNELS,
                 normalize_volume: bool = True,
                 remove_silence: bool = False,
                 min_duration: float = 0.5,
                 max_duration: float = 300.0):
        """
        初始化转换器
        
        Args:
            sample_rate: 目标采样率
            channels: 目标声道数
            normalize_volume: 是否标准化音量
            remove_silence: 是否去除静音
            min_duration: 最小音频长度（秒）
            max_duration: 最大音频长度（秒）
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.normalize_volume = normalize_volume
        self.remove_silence = remove_silence
        self.min_duration = min_duration
        self.max_duration = max_duration
    
    def is_supported_format(self, file_path: Path) -> bool:
        """检查文件格式是否支持"""
        suffix = file_path.suffix.lower()
        return suffix in SUPPORTED_VIDEO_FORMATS or suffix in SUPPORTED_AUDIO_FORMATS
    
    def extract_audio_with_ffmpeg(self, 
                                  input_path: Path, 
                                  output_path: Path,
                                  start_time: Optional[float] = None,
                                  duration: Optional[float] = None) -> bool:
        """
        使用 ffmpeg 提取音频
        
        Args:
            input_path: 输入视频文件路径
            output_path: 输出音频文件路径
            start_time: 开始时间（秒）
            duration: 持续时间（秒）
            
        Returns:
            bool: 是否成功
        """
        try:
            # 构建 ffmpeg 命令
            input_stream = ffmpeg.input(str(input_path))
            
            # 如果指定了时间段
            if start_time is not None:
                input_stream = input_stream.filter('atrim', start=start_time)
            if duration is not None:
                input_stream = input_stream.filter('atrim', duration=duration)
            
            # 音频处理参数
            audio_stream = input_stream.audio
            
            # 设置采样率和声道
            audio_stream = ffmpeg.filter(audio_stream, 'aresample', self.sample_rate)
            audio_stream = ffmpeg.filter(audio_stream, 'aformat', channel_layouts='mono')
            
            # 音量标准化
            if self.normalize_volume:
                audio_stream = ffmpeg.filter(audio_stream, 'loudnorm')
            
            # 输出设置
            output_stream = ffmpeg.output(
                audio_stream, 
                str(output_path),
                acodec='pcm_s16le',  # 16-bit PCM
                ar=self.sample_rate,
                ac=self.channels,
                y=None  # 不覆盖已存在的文件，会提示
            )
            
            # 执行转换
            ffmpeg.run(output_stream, capture_stdout=True, capture_stderr=True)
            return True
            
        except ffmpeg.Error as e:
            logger.error(f"FFmpeg 错误: {e.stderr.decode() if e.stderr else str(e)}")
            return False
        except Exception as e:
            logger.error(f"转换失败: {str(e)}")
            return False
    
    def post_process_audio(self, audio_path: Path) -> bool:
        """
        后处理音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 加载音频
            audio, sr = librosa.load(str(audio_path), sr=self.sample_rate, mono=True)
            
            # 检查音频长度
            duration = len(audio) / sr
            if duration < self.min_duration:
                logger.warning(f"音频太短 ({duration:.2f}s < {self.min_duration}s): {audio_path}")
                return False
            
            if duration > self.max_duration:
                logger.warning(f"音频太长 ({duration:.2f}s > {self.max_duration}s)，将截取前 {self.max_duration}s")
                audio = audio[:int(self.max_duration * sr)]
            
            # 去除静音
            if self.remove_silence:
                audio, _ = librosa.effects.trim(audio, top_db=20)
            
            # 确保音频值在 [-1, 1] 范围内
            if np.max(np.abs(audio)) > 1.0:
                audio = audio / np.max(np.abs(audio)) * 0.95
            
            # 保存处理后的音频
            sf.write(str(audio_path), audio, sr, subtype='PCM_16')
            
            logger.info(f"音频后处理完成: {audio_path} (时长: {len(audio)/sr:.2f}s)")
            return True
            
        except Exception as e:
            logger.error(f"音频后处理失败: {str(e)}")
            return False
    
    def convert_single_file(self, 
                           input_path: Path, 
                           output_path: Optional[Path] = None,
                           start_time: Optional[float] = None,
                           duration: Optional[float] = None) -> bool:
        """
        转换单个文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径（可选）
            start_time: 开始时间（秒）
            duration: 持续时间（秒）
            
        Returns:
            bool: 是否成功
        """
        if not input_path.exists():
            logger.error(f"输入文件不存在: {input_path}")
            return False
        
        if not self.is_supported_format(input_path):
            logger.error(f"不支持的文件格式: {input_path.suffix}")
            return False
        
        # 确定输出路径
        if output_path is None:
            output_path = input_path.parent / f"{input_path.stem}_audio.wav"
        
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"开始转换: {input_path} -> {output_path}")
        
        # 提取音频
        if not self.extract_audio_with_ffmpeg(input_path, output_path, start_time, duration):
            return False
        
        # 后处理音频
        if not self.post_process_audio(output_path):
            # 如果后处理失败，删除输出文件
            if output_path.exists():
                output_path.unlink()
            return False
        
        logger.info(f"转换成功: {output_path}")
        return True
    
    def convert_batch(self, 
                     input_dir: Path, 
                     output_dir: Optional[Path] = None,
                     recursive: bool = True) -> Tuple[int, int]:
        """
        批量转换
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录（可选）
            recursive: 是否递归搜索子目录
            
        Returns:
            Tuple[int, int]: (成功数量, 总数量)
        """
        if not input_dir.exists() or not input_dir.is_dir():
            logger.error(f"输入目录不存在或不是目录: {input_dir}")
            return 0, 0
        
        # 确定输出目录
        if output_dir is None:
            output_dir = input_dir / "audio_output"
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 查找所有支持的文件
        pattern = "**/*" if recursive else "*"
        all_files = []
        
        for ext in SUPPORTED_VIDEO_FORMATS | SUPPORTED_AUDIO_FORMATS:
            all_files.extend(input_dir.glob(f"{pattern}{ext}"))
        
        if not all_files:
            logger.warning(f"在目录中未找到支持的文件: {input_dir}")
            return 0, 0
        
        logger.info(f"找到 {len(all_files)} 个文件待转换")
        
        success_count = 0
        
        # 批量转换
        for input_file in tqdm(all_files, desc="转换进度"):
            # 构建输出文件路径，保持目录结构
            relative_path = input_file.relative_to(input_dir)
            output_file = output_dir / relative_path.with_suffix('.wav')
            
            if self.convert_single_file(input_file, output_file):
                success_count += 1
        
        logger.info(f"批量转换完成: {success_count}/{len(all_files)} 个文件成功")
        return success_count, len(all_files)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="视频转音频工具 - 专为 IndexTTS 项目优化",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 转换单个视频文件
  python video_to_audio.py input.mp4
  
  # 指定输出文件
  python video_to_audio.py input.mp4 -o output.wav
  
  # 截取时间段 (从30秒开始，持续60秒)
  python video_to_audio.py input.mp4 -s 30 -d 60
  
  # 批量转换目录中的所有视频
  python video_to_audio.py -b /path/to/videos -o /path/to/output
  
  # 递归转换子目录
  python video_to_audio.py -b /path/to/videos -r
        """
    )
    
    # 输入参数
    parser.add_argument('input', nargs='?', help='输入视频文件或目录路径')
    parser.add_argument('-o', '--output', help='输出音频文件或目录路径')
    parser.add_argument('-b', '--batch', action='store_true', help='批量处理模式')
    parser.add_argument('-r', '--recursive', action='store_true', help='递归搜索子目录')
    
    # 时间参数
    parser.add_argument('-s', '--start', type=float, help='开始时间（秒）')
    parser.add_argument('-d', '--duration', type=float, help='持续时间（秒）')
    
    # 音频参数
    parser.add_argument('--sample-rate', type=int, default=TARGET_SAMPLE_RATE, 
                       help=f'采样率 (默认: {TARGET_SAMPLE_RATE})')
    parser.add_argument('--no-normalize', action='store_true', help='不进行音量标准化')
    parser.add_argument('--remove-silence', action='store_true', help='去除开头结尾的静音')
    parser.add_argument('--min-duration', type=float, default=0.5, help='最小音频长度（秒）')
    parser.add_argument('--max-duration', type=float, default=300.0, help='最大音频长度（秒）')
    
    # 其他参数
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--version', action='version', version='%(prog)s 1.0.0')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查输入参数
    if not args.input:
        parser.print_help()
        return 1
    
    input_path = Path(args.input)
    output_path = Path(args.output) if args.output else None
    
    # 创建转换器
    converter = VideoToAudioConverter(
        sample_rate=args.sample_rate,
        normalize_volume=not args.no_normalize,
        remove_silence=args.remove_silence,
        min_duration=args.min_duration,
        max_duration=args.max_duration
    )
    
    try:
        if args.batch:
            # 批量处理模式
            success, total = converter.convert_batch(
                input_path, 
                output_path, 
                args.recursive
            )
            if success == total:
                logger.info("所有文件转换成功！")
                return 0
            else:
                logger.warning(f"部分文件转换失败: {success}/{total}")
                return 1
        else:
            # 单文件处理模式
            if converter.convert_single_file(
                input_path, 
                output_path, 
                args.start, 
                args.duration
            ):
                logger.info("文件转换成功！")
                return 0
            else:
                logger.error("文件转换失败！")
                return 1
                
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
