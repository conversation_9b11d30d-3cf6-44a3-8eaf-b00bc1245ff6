#!/usr/bin/env python3
"""
简单视频转音频工具 - IndexTTS 专用

使用更简单的方法，避免复杂的 FFmpeg 滤镜链问题
"""

import os
import sys
import subprocess
from pathlib import Path
import argparse


def convert_video_to_audio_simple(input_path, output_path=None, start_time=None, duration=None):
    """
    使用简单的 FFmpeg 命令转换视频到音频
    
    Args:
        input_path: 输入视频文件路径
        output_path: 输出音频文件路径（可选）
        start_time: 开始时间（秒）
        duration: 持续时间（秒）
    
    Returns:
        bool: 是否成功
    """
    input_path = Path(input_path)
    
    if not input_path.exists():
        print(f"❌ 输入文件不存在: {input_path}")
        return False
    
    # 确定输出路径
    if output_path is None:
        output_path = input_path.parent / f"{input_path.stem}_for_indextts.wav"
    else:
        output_path = Path(output_path)
    
    # 确保输出目录存在
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    print(f"🎬 输入: {input_path}")
    print(f"🎵 输出: {output_path}")
    
    try:
        # 构建 FFmpeg 命令
        cmd = ['ffmpeg', '-y']  # -y 表示覆盖输出文件
        
        # 输入文件
        cmd.extend(['-i', str(input_path)])
        
        # 时间参数
        if start_time is not None:
            print(f"⏰ 开始时间: {start_time}秒")
            cmd.extend(['-ss', str(start_time)])
        
        if duration is not None:
            print(f"⏱️  持续时间: {duration}秒")
            cmd.extend(['-t', str(duration)])
        
        # 音频参数
        cmd.extend([
            '-vn',           # 不要视频
            '-acodec', 'pcm_s16le',  # 16-bit PCM 编码
            '-ar', '22050',  # 采样率 22050 Hz
            '-ac', '1',      # 单声道
            '-af', 'loudnorm',  # 音量标准化
            str(output_path)
        ])
        
        print("🔄 开始转换...")
        print(f"🔧 命令: {' '.join(cmd)}")
        
        # 执行转换
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 转换成功: {output_path}")
            
            # 显示文件信息
            if output_path.exists():
                size_mb = output_path.stat().st_size / (1024 * 1024)
                print(f"📁 文件大小: {size_mb:.2f} MB")
            
            return True
        else:
            print(f"❌ FFmpeg 错误:")
            print(result.stderr)
            return False
        
    except FileNotFoundError:
        print("❌ 未找到 ffmpeg，请确保已安装 FFmpeg")
        return False
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False


def batch_convert_simple(input_dir, output_dir=None):
    """
    批量转换目录中的所有视频文件
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录（可选）
    
    Returns:
        tuple: (成功数量, 总数量)
    """
    input_dir = Path(input_dir)
    
    if not input_dir.exists() or not input_dir.is_dir():
        print(f"❌ 输入目录不存在: {input_dir}")
        return 0, 0
    
    # 支持的视频格式
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
    
    # 查找所有视频文件
    video_files = []
    for ext in video_extensions:
        video_files.extend(input_dir.glob(f"*{ext}"))
        video_files.extend(input_dir.glob(f"*{ext.upper()}"))
    
    if not video_files:
        print(f"❌ 在目录中未找到视频文件: {input_dir}")
        return 0, 0
    
    print(f"📂 找到 {len(video_files)} 个视频文件")
    
    # 确定输出目录
    if output_dir is None:
        output_dir = input_dir / "audio_for_indextts"
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    success_count = 0
    
    for i, video_file in enumerate(video_files, 1):
        print(f"\n📹 [{i}/{len(video_files)}] 处理: {video_file.name}")
        
        output_file = output_dir / f"{video_file.stem}.wav"
        
        if convert_video_to_audio_simple(video_file, output_file):
            success_count += 1
        else:
            print(f"❌ 跳过: {video_file.name}")
    
    print(f"\n🎉 批量转换完成: {success_count}/{len(video_files)} 个文件成功")
    return success_count, len(video_files)


def check_audio_properties(audio_path):
    """
    检查音频文件的属性
    
    Args:
        audio_path: 音频文件路径
    """
    try:
        import librosa
        audio, sr = librosa.load(str(audio_path), sr=None)
        duration = len(audio) / sr
        
        print(f"📊 音频信息:")
        print(f"   - 采样率: {sr} Hz")
        print(f"   - 时长: {duration:.2f} 秒")
        print(f"   - 样本数: {len(audio)}")
        print(f"   - 声道: {'单声道' if len(audio.shape) == 1 else '多声道'}")
        
        # 验证参数
        if sr == 22050:
            print("✅ 采样率正确 (22050 Hz)")
        else:
            print(f"⚠️  采样率: 期望 22050 Hz，实际 {sr} Hz")
        
        if len(audio.shape) == 1:
            print("✅ 单声道正确")
        else:
            print("⚠️  声道数: 应该是单声道")
            
    except ImportError:
        print("⚠️  无法导入 librosa，跳过音频属性检查")
    except Exception as e:
        print(f"❌ 音频属性检查失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="简单视频转音频工具 - IndexTTS 专用",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 转换单个视频
  python simple_video_to_audio.py video.mp4
  
  # 指定输出文件
  python simple_video_to_audio.py video.mp4 -o audio.wav
  
  # 截取片段 (从30秒开始，持续10秒)
  python simple_video_to_audio.py video.mp4 -s 30 -d 10
  
  # 批量转换目录
  python simple_video_to_audio.py -b /path/to/videos
  
  # 检查音频属性
  python simple_video_to_audio.py video.mp4 --check
        """
    )
    
    parser.add_argument('input', nargs='?', help='输入视频文件或目录路径')
    parser.add_argument('-o', '--output', help='输出音频文件或目录路径')
    parser.add_argument('-b', '--batch', action='store_true', help='批量处理模式')
    parser.add_argument('-s', '--start', type=float, help='开始时间（秒）')
    parser.add_argument('-d', '--duration', type=float, help='持续时间（秒）')
    parser.add_argument('--check', action='store_true', help='检查输出音频的属性')
    
    args = parser.parse_args()
    
    if not args.input:
        parser.print_help()
        return 1
    
    try:
        if args.batch:
            # 批量处理
            success, total = batch_convert_simple(args.input, args.output)
            return 0 if success == total else 1
        else:
            # 单文件处理
            if convert_video_to_audio_simple(args.input, args.output, args.start, args.duration):
                # 如果需要检查音频属性
                if args.check:
                    output_path = Path(args.output) if args.output else Path(args.input).parent / f"{Path(args.input).stem}_for_indextts.wav"
                    if output_path.exists():
                        check_audio_properties(output_path)
                return 0
            else:
                return 1
    
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
