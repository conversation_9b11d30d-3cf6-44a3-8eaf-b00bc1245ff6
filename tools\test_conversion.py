#!/usr/bin/env python3
"""
测试视频转音频工具的功能
"""

import os
import sys
from pathlib import Path
import tempfile
import subprocess

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_test_video(output_path, duration=5):
    """
    创建一个测试视频文件
    
    Args:
        output_path: 输出路径
        duration: 视频长度（秒）
    
    Returns:
        bool: 是否成功
    """
    try:
        # 使用 ffmpeg 创建一个简单的测试视频
        cmd = [
            'ffmpeg', '-y',  # 覆盖已存在的文件
            '-f', 'lavfi',   # 使用 lavfi 输入
            '-i', f'testsrc=duration={duration}:size=320x240:rate=30',  # 视频源
            '-f', 'lavfi',   # 音频源
            '-i', f'sine=frequency=1000:duration={duration}',  # 1000Hz 正弦波
            '-c:v', 'libx264',  # 视频编码器
            '-c:a', 'aac',      # 音频编码器
            '-shortest',        # 以最短流为准
            str(output_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 测试视频创建成功: {output_path}")
            return True
        else:
            print(f"❌ 测试视频创建失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ 未找到 ffmpeg，无法创建测试视频")
        return False
    except Exception as e:
        print(f"❌ 创建测试视频时出错: {str(e)}")
        return False


def test_single_conversion():
    """测试单个文件转换"""
    print("\n" + "="*50)
    print("🧪 测试：单个文件转换")
    print("="*50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试视频
        test_video = temp_path / "test_video.mp4"
        if not create_test_video(test_video, duration=3):
            return False
        
        # 测试转换
        from tools.quick_video_to_audio import convert_video_to_audio
        
        output_audio = temp_path / "test_audio.wav"
        
        print(f"🔄 转换视频: {test_video} -> {output_audio}")
        
        if convert_video_to_audio(str(test_video), str(output_audio)):
            # 检查输出文件
            if output_audio.exists():
                size = output_audio.stat().st_size
                print(f"✅ 转换成功，文件大小: {size} 字节")
                
                # 检查音频属性
                try:
                    import librosa
                    audio, sr = librosa.load(str(output_audio), sr=None)
                    duration = len(audio) / sr
                    
                    print(f"📊 音频信息:")
                    print(f"   - 采样率: {sr} Hz")
                    print(f"   - 时长: {duration:.2f} 秒")
                    print(f"   - 样本数: {len(audio)}")
                    print(f"   - 声道: {'单声道' if len(audio.shape) == 1 else '多声道'}")
                    
                    # 验证参数是否正确
                    if sr == 22050:
                        print("✅ 采样率正确 (22050 Hz)")
                    else:
                        print(f"❌ 采样率错误，期望 22050 Hz，实际 {sr} Hz")
                    
                    if len(audio.shape) == 1:
                        print("✅ 单声道正确")
                    else:
                        print("❌ 声道数错误，应该是单声道")
                    
                    return True
                    
                except ImportError:
                    print("⚠️  无法导入 librosa，跳过音频属性检查")
                    return True
                except Exception as e:
                    print(f"❌ 音频属性检查失败: {str(e)}")
                    return False
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 转换失败")
            return False


def test_time_segment():
    """测试时间段截取"""
    print("\n" + "="*50)
    print("🧪 测试：时间段截取")
    print("="*50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建较长的测试视频
        test_video = temp_path / "long_test_video.mp4"
        if not create_test_video(test_video, duration=10):
            return False
        
        from tools.quick_video_to_audio import convert_video_to_audio
        
        # 测试截取前3秒
        output_audio = temp_path / "segment_audio.wav"
        
        print(f"🔄 截取前3秒: {test_video} -> {output_audio}")
        
        if convert_video_to_audio(str(test_video), str(output_audio), start_time=0, duration=3):
            if output_audio.exists():
                try:
                    import librosa
                    audio, sr = librosa.load(str(output_audio), sr=None)
                    duration = len(audio) / sr
                    
                    print(f"📊 截取后音频时长: {duration:.2f} 秒")
                    
                    # 检查时长是否接近3秒（允许一定误差）
                    if 2.5 <= duration <= 3.5:
                        print("✅ 时间段截取正确")
                        return True
                    else:
                        print(f"❌ 时间段截取错误，期望约3秒，实际 {duration:.2f} 秒")
                        return False
                        
                except ImportError:
                    print("⚠️  无法导入 librosa，跳过时长检查")
                    return True
                except Exception as e:
                    print(f"❌ 时长检查失败: {str(e)}")
                    return False
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 时间段截取失败")
            return False


def test_batch_conversion():
    """测试批量转换"""
    print("\n" + "="*50)
    print("🧪 测试：批量转换")
    print("="*50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建多个测试视频
        test_videos = []
        for i in range(3):
            video_path = temp_path / f"test_video_{i+1}.mp4"
            if create_test_video(video_path, duration=2):
                test_videos.append(video_path)
        
        if len(test_videos) == 0:
            print("❌ 无法创建测试视频")
            return False
        
        print(f"📁 创建了 {len(test_videos)} 个测试视频")
        
        # 测试批量转换
        from tools.quick_video_to_audio import batch_convert
        
        output_dir = temp_path / "batch_output"
        
        print(f"🔄 批量转换: {temp_path} -> {output_dir}")
        
        success, total = batch_convert(str(temp_path), str(output_dir))
        
        print(f"📊 转换结果: {success}/{total}")
        
        if success == len(test_videos):
            # 检查输出文件
            audio_files = list(output_dir.glob("*.wav"))
            print(f"📁 生成了 {len(audio_files)} 个音频文件")
            
            if len(audio_files) == len(test_videos):
                print("✅ 批量转换成功")
                return True
            else:
                print("❌ 输出文件数量不匹配")
                return False
        else:
            print("❌ 批量转换失败")
            return False


def test_error_handling():
    """测试错误处理"""
    print("\n" + "="*50)
    print("🧪 测试：错误处理")
    print("="*50)
    
    from tools.quick_video_to_audio import convert_video_to_audio
    
    # 测试不存在的文件
    print("🔄 测试不存在的文件...")
    result = convert_video_to_audio("nonexistent_file.mp4")
    if not result:
        print("✅ 正确处理了不存在的文件")
    else:
        print("❌ 应该返回失败但返回了成功")
        return False
    
    # 测试无效的时间参数
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_video = temp_path / "test_video.mp4"
        
        if create_test_video(test_video, duration=5):
            print("🔄 测试无效的时间参数...")
            output_audio = temp_path / "invalid_time_audio.wav"
            
            # 尝试从超出视频长度的时间开始
            result = convert_video_to_audio(
                str(test_video), 
                str(output_audio), 
                start_time=100,  # 超出视频长度
                duration=5
            )
            
            # 这种情况下，ffmpeg 可能会成功但生成空文件或很短的文件
            print("✅ 错误处理测试完成")
    
    return True


def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行视频转音频工具测试")
    print("="*60)
    
    tests = [
        ("单个文件转换", test_single_conversion),
        ("时间段截取", test_time_segment),
        ("批量转换", test_batch_conversion),
        ("错误处理", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🚀 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！工具运行正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查工具配置。")
        return False


if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行出错: {str(e)}")
        sys.exit(1)
