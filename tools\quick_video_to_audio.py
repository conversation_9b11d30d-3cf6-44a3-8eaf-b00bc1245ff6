#!/usr/bin/env python3
"""
快速视频转音频工具 - IndexTTS 专用

简化版本，快速将视频转换为 IndexTTS 需要的音频格式
"""

import os
import sys
from pathlib import Path
import ffmpeg
import argparse


def convert_video_to_audio(input_path, output_path=None, start_time=None, duration=None):
    """
    将视频转换为 IndexTTS 需要的音频格式
    
    Args:
        input_path: 输入视频文件路径
        output_path: 输出音频文件路径（可选）
        start_time: 开始时间（秒）
        duration: 持续时间（秒）
    
    Returns:
        bool: 是否成功
    """
    input_path = Path(input_path)
    
    if not input_path.exists():
        print(f"❌ 输入文件不存在: {input_path}")
        return False
    
    # 确定输出路径
    if output_path is None:
        output_path = input_path.parent / f"{input_path.stem}_for_indextts.wav"
    else:
        output_path = Path(output_path)
    
    # 确保输出目录存在
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    print(f"🎬 输入: {input_path}")
    print(f"🎵 输出: {output_path}")
    
    try:
        # 构建 ffmpeg 命令
        input_stream = ffmpeg.input(str(input_path))
        
        # 时间段截取
        if start_time is not None:
            print(f"⏰ 开始时间: {start_time}秒")
            input_stream = input_stream.filter('atrim', start=start_time)
        
        if duration is not None:
            print(f"⏱️  持续时间: {duration}秒")
            input_stream = input_stream.filter('atrim', duration=duration)
        
        # 音频处理：转换为 IndexTTS 需要的格式
        audio_stream = input_stream.audio

        # 重采样到 22050 Hz
        audio_stream = ffmpeg.filter(audio_stream, 'aresample', 22050)

        # 转换为单声道
        audio_stream = ffmpeg.filter(audio_stream, 'aformat', channel_layouts='mono')

        # 音量标准化
        audio_stream = ffmpeg.filter(audio_stream, 'loudnorm')
        
        # 输出设置
        output_stream = ffmpeg.output(
            audio_stream,
            str(output_path),
            acodec='pcm_s16le',  # 16-bit PCM
            ar=22050,  # 采样率
            ac=1,      # 单声道
            y=None     # 询问是否覆盖
        )
        
        print("🔄 开始转换...")
        
        # 执行转换
        ffmpeg.run(output_stream, capture_stdout=True, capture_stderr=True)
        
        print(f"✅ 转换成功: {output_path}")
        
        # 显示文件信息
        if output_path.exists():
            size_mb = output_path.stat().st_size / (1024 * 1024)
            print(f"📁 文件大小: {size_mb:.2f} MB")
        
        return True
        
    except ffmpeg.Error as e:
        error_msg = e.stderr.decode() if e.stderr else str(e)
        print(f"❌ FFmpeg 错误: {error_msg}")
        return False
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False


def batch_convert(input_dir, output_dir=None):
    """
    批量转换目录中的所有视频文件
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录（可选）
    
    Returns:
        tuple: (成功数量, 总数量)
    """
    input_dir = Path(input_dir)
    
    if not input_dir.exists() or not input_dir.is_dir():
        print(f"❌ 输入目录不存在: {input_dir}")
        return 0, 0
    
    # 支持的视频格式
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
    
    # 查找所有视频文件
    video_files = []
    for ext in video_extensions:
        video_files.extend(input_dir.glob(f"*{ext}"))
        video_files.extend(input_dir.glob(f"*{ext.upper()}"))
    
    if not video_files:
        print(f"❌ 在目录中未找到视频文件: {input_dir}")
        return 0, 0
    
    print(f"📂 找到 {len(video_files)} 个视频文件")
    
    # 确定输出目录
    if output_dir is None:
        output_dir = input_dir / "audio_for_indextts"
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    success_count = 0
    
    for i, video_file in enumerate(video_files, 1):
        print(f"\n📹 [{i}/{len(video_files)}] 处理: {video_file.name}")
        
        output_file = output_dir / f"{video_file.stem}.wav"
        
        if convert_video_to_audio(video_file, output_file):
            success_count += 1
        else:
            print(f"❌ 跳过: {video_file.name}")
    
    print(f"\n🎉 批量转换完成: {success_count}/{len(video_files)} 个文件成功")
    return success_count, len(video_files)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="快速视频转音频工具 - IndexTTS 专用",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 转换单个视频
  python quick_video_to_audio.py video.mp4
  
  # 指定输出文件
  python quick_video_to_audio.py video.mp4 -o audio.wav
  
  # 截取片段 (从30秒开始，持续10秒)
  python quick_video_to_audio.py video.mp4 -s 30 -d 10
  
  # 批量转换目录
  python quick_video_to_audio.py -b /path/to/videos
  
  # 批量转换并指定输出目录
  python quick_video_to_audio.py -b /path/to/videos -o /path/to/output
        """
    )
    
    parser.add_argument('input', nargs='?', help='输入视频文件或目录路径')
    parser.add_argument('-o', '--output', help='输出音频文件或目录路径')
    parser.add_argument('-b', '--batch', action='store_true', help='批量处理模式')
    parser.add_argument('-s', '--start', type=float, help='开始时间（秒）')
    parser.add_argument('-d', '--duration', type=float, help='持续时间（秒）')
    
    args = parser.parse_args()
    
    if not args.input:
        parser.print_help()
        return 1
    
    try:
        if args.batch:
            # 批量处理
            success, total = batch_convert(args.input, args.output)
            return 0 if success == total else 1
        else:
            # 单文件处理
            if convert_video_to_audio(args.input, args.output, args.start, args.duration):
                return 0
            else:
                return 1
    
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
