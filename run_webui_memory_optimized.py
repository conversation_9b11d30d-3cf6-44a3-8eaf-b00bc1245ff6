#!/usr/bin/env python3
"""
IndexTTS WebUI 内存优化启动脚本

这个脚本使用内存优化设置启动 IndexTTS WebUI
"""

import os
import sys
import subprocess

def main():
    """启动内存优化的 WebUI"""
    print("🧠 启动 IndexTTS WebUI (内存优化模式)")
    print("=" * 50)
    print("⚡ 使用 CPU 模式 + 内存优化设置")
    print("📏 建议使用 5-15 秒的短音频片段")
    print("=" * 50)
    
    # 设置环境变量进行内存优化
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = ''  # 强制 CPU 模式
    env['OMP_NUM_THREADS'] = '4'      # 限制 OpenMP 线程数
    env['MKL_NUM_THREADS'] = '4'      # 限制 MKL 线程数
    env['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'  # 限制内存分配
    
    # 构建启动命令
    cmd = [
        sys.executable, 'webui.py',
        '--cpu',           # 强制 CPU 模式
        '--verbose',       # 详细输出
        '--gui_seg_tokens', '60',  # 减少每次生成的 token 数量
    ]
    
    print(f"🚀 启动命令: {' '.join(cmd)}")
    print("🌐 WebUI 将在 http://localhost:7860 启动")
    print("⏹️  按 Ctrl+C 停止服务")
    print()
    print("💡 使用提示:")
    print("   - 使用分割后的短音频片段（10秒以内）")
    print("   - 避免同时处理多个请求")
    print("   - 如果仍然内存不足，请重启程序")
    print()
    
    try:
        # 启动 WebUI
        subprocess.run(cmd, env=env, check=True)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断，正在停止服务...")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
