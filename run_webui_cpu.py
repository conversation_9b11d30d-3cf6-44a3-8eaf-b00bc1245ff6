#!/usr/bin/env python3
"""
IndexTTS WebUI CPU 模式启动脚本

这个脚本强制使用 CPU 模式运行 IndexTTS WebUI，避免 CUDA 相关问题
"""

import os
import sys
import subprocess

def main():
    """启动 CPU 模式的 WebUI"""
    print("🖥️  启动 IndexTTS WebUI (CPU 模式)")
    print("=" * 50)
    print("⚠️  注意：CPU 模式运行速度较慢，但更稳定")
    print("🔧 如果遇到 CUDA 错误，这是推荐的解决方案")
    print("=" * 50)
    
    # 设置环境变量强制使用 CPU
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = ''  # 隐藏所有 GPU
    
    # 构建启动命令
    cmd = [
        sys.executable, 'webui.py',
        '--cpu',  # 强制 CPU 模式
        '--verbose',  # 详细输出
    ]
    
    print(f"🚀 启动命令: {' '.join(cmd)}")
    print("🌐 WebUI 将在 http://localhost:7860 启动")
    print("⏹️  按 Ctrl+C 停止服务")
    print()
    
    try:
        # 启动 WebUI
        subprocess.run(cmd, env=env, check=True)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断，正在停止服务...")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
