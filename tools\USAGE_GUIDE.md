# 视频转音频工具使用指南

## 🎯 快速开始

如果你有视频文件需要转换为 IndexTTS 可用的音频，推荐使用 `simple_video_to_audio.py`：

```bash
# 在 IndexTTS 项目目录中运行
uv run python tools/simple_video_to_audio.py your_video.mp4
```

这会生成 `your_video_for_indextts.wav` 文件，可以直接在 IndexTTS 中使用。

## 📋 工具选择

### 🌟 推荐：`simple_video_to_audio.py`
- **优点**：兼容性最好，使用标准 FFmpeg 命令
- **适用**：大多数情况下的首选
- **特点**：简单可靠，错误处理好

### 🚀 备选：`quick_video_to_audio.py`
- **优点**：功能丰富，使用 ffmpeg-python 库
- **适用**：需要复杂处理的场景
- **特点**：更多自定义选项

### 🔧 高级：`video_to_audio.py`
- **优点**：功能最全面，支持高级选项
- **适用**：专业用户，需要精细控制
- **特点**：音频后处理，质量检查

## 🎬 常见使用场景

### 场景1：转换单个视频文件

```bash
# 基本转换
uv run python tools/simple_video_to_audio.py movie.mp4

# 指定输出文件名
uv run python tools/simple_video_to_audio.py movie.mp4 -o voice_sample.wav

# 检查转换后的音频属性
uv run python tools/simple_video_to_audio.py movie.mp4 --check
```

### 场景2：截取视频片段

```bash
# 从第30秒开始，截取10秒的音频
uv run python tools/simple_video_to_audio.py movie.mp4 -s 30 -d 10 -o clip.wav

# 截取开头5秒
uv run python tools/simple_video_to_audio.py movie.mp4 -s 0 -d 5 -o intro.wav
```

### 场景3：批量转换

```bash
# 转换整个目录的视频文件
uv run python tools/simple_video_to_audio.py -b ./videos

# 指定输出目录
uv run python tools/simple_video_to_audio.py -b ./videos -o ./audio_output
```

### 场景4：在 IndexTTS 中使用

```python
from indextts.infer_v2 import IndexTTS2

# 初始化 IndexTTS
tts = IndexTTS2(
    cfg_path="checkpoints/config.yaml", 
    model_dir="checkpoints"
)

# 使用转换后的音频
tts.infer(
    spk_audio_prompt='movie_for_indextts.wav',  # 转换后的音频
    text="你好，这是语音合成测试。",
    output_path="generated.wav"
)
```

## 🔧 技术规格

转换后的音频文件规格：
- **采样率**：22050 Hz
- **声道**：单声道 (Mono)
- **格式**：WAV (16-bit PCM)
- **音量**：已标准化

## 📁 文件组织建议

```
your_project/
├── videos/                 # 原始视频文件
│   ├── video1.mp4
│   ├── video2.avi
│   └── video3.mkv
├── audio_for_indextts/     # 转换后的音频
│   ├── video1.wav
│   ├── video2.wav
│   └── video3.wav
└── generated_speech/       # IndexTTS 生成的语音
    ├── output1.wav
    ├── output2.wav
    └── output3.wav
```

## 🚨 常见问题解决

### Q1: 提示找不到 ffmpeg
```bash
# Windows (使用 chocolatey)
choco install ffmpeg

# macOS (使用 homebrew)
brew install ffmpeg

# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg
```

### Q2: 转换失败或音质不好
```bash
# 检查原视频是否有音轨
ffmpeg -i your_video.mp4

# 使用更简单的命令
ffmpeg -i your_video.mp4 -vn -acodec pcm_s16le -ar 22050 -ac 1 output.wav
```

### Q3: 批量转换时部分文件失败
```bash
# 检查文件格式是否支持
# 支持的格式：MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V

# 如果格式不支持，先转换为 MP4
ffmpeg -i unsupported_video.xxx -c copy video.mp4
```

### Q4: 音频太长或太短
```bash
# 截取特定时间段
uv run python tools/simple_video_to_audio.py video.mp4 -s 10 -d 30

# 对于很长的视频，建议分段处理
```

## 🎯 最佳实践

### 1. 音频质量优化
- 选择音质较好的原视频
- 避免背景音乐过大的视频
- 优先选择人声清晰的片段

### 2. 时间段选择
- 选择说话人声音稳定的片段
- 避免有回声或噪音的部分
- 建议单次录音长度 3-30 秒

### 3. 批量处理
- 按说话人分类组织视频文件
- 使用描述性的文件名
- 定期清理不需要的文件

### 4. IndexTTS 集成
- 测试转换后的音频质量
- 根据效果调整音频片段
- 保存效果好的音频样本

## 📊 性能参考

| 视频长度 | 转换时间 | 输出大小 |
|---------|---------|---------|
| 1分钟   | ~5秒    | ~2.6MB   |
| 5分钟   | ~15秒   | ~13MB    |
| 30分钟  | ~1.5分钟 | ~79MB    |

*注：实际性能取决于硬件配置和视频复杂度*

## 🔄 工作流程示例

```bash
# 1. 准备视频文件
mkdir videos && cp *.mp4 videos/

# 2. 批量转换
uv run python tools/simple_video_to_audio.py -b videos -o audio_samples

# 3. 检查音频质量
uv run python tools/simple_video_to_audio.py audio_samples/sample1.wav --check

# 4. 在 IndexTTS 中测试
uv run python -c "
from indextts.infer_v2 import IndexTTS2
tts = IndexTTS2('checkpoints/config.yaml', 'checkpoints')
tts.infer('audio_samples/sample1.wav', '测试文本', 'test_output.wav')
"

# 5. 根据效果调整和优化
```

## 📞 获取帮助

如果遇到问题：

1. **查看帮助信息**：
   ```bash
   uv run python tools/simple_video_to_audio.py --help
   ```

2. **检查 FFmpeg 安装**：
   ```bash
   ffmpeg -version
   ```

3. **测试基本功能**：
   ```bash
   uv run python tools/test_conversion.py
   ```

4. **查看详细文档**：
   ```bash
   cat tools/README_video_to_audio.md
   ```
