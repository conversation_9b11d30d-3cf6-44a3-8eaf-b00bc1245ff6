#!/usr/bin/env python3
"""
音频分段工具 - 为 IndexTTS 优化

将长音频文件分割成适合 IndexTTS 处理的短片段
"""

import os
import sys
import argparse
from pathlib import Path
import librosa
import soundfile as sf
import numpy as np


def split_audio(input_path, output_dir=None, segment_length=10, overlap=1, min_segment=3):
    """
    将音频文件分割成多个短片段
    
    Args:
        input_path: 输入音频文件路径
        output_dir: 输出目录
        segment_length: 每段长度（秒）
        overlap: 重叠时间（秒）
        min_segment: 最小片段长度（秒）
    
    Returns:
        list: 生成的音频片段路径列表
    """
    input_path = Path(input_path)
    
    if not input_path.exists():
        print(f"❌ 输入文件不存在: {input_path}")
        return []
    
    # 确定输出目录
    if output_dir is None:
        output_dir = input_path.parent / f"{input_path.stem}_segments"
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"🎵 输入文件: {input_path}")
    print(f"📁 输出目录: {output_dir}")
    print(f"⏱️  片段长度: {segment_length} 秒")
    print(f"🔄 重叠时间: {overlap} 秒")
    
    try:
        # 加载音频
        print("🔄 加载音频文件...")
        audio, sr = librosa.load(str(input_path), sr=22050)  # IndexTTS 使用 22050Hz
        duration = len(audio) / sr
        
        print(f"📊 音频信息:")
        print(f"   - 时长: {duration:.2f} 秒")
        print(f"   - 采样率: {sr} Hz")
        print(f"   - 样本数: {len(audio)}")
        
        if duration <= segment_length:
            print(f"✅ 音频已经足够短 ({duration:.2f}s <= {segment_length}s)，无需分割")
            # 直接复制到输出目录
            output_path = output_dir / f"{input_path.stem}_segment_001.wav"
            sf.write(str(output_path), audio, sr)
            return [output_path]
        
        # 计算分段参数
        step_size = segment_length - overlap  # 实际步进大小
        segment_samples = int(segment_length * sr)
        step_samples = int(step_size * sr)
        
        segments = []
        segment_count = 0
        
        # 分割音频
        start_sample = 0
        while start_sample < len(audio):
            end_sample = min(start_sample + segment_samples, len(audio))
            
            # 检查片段长度
            segment_duration = (end_sample - start_sample) / sr
            if segment_duration < min_segment:
                print(f"⚠️  跳过过短片段: {segment_duration:.2f}s < {min_segment}s")
                break
            
            # 提取片段
            segment_audio = audio[start_sample:end_sample]
            
            # 保存片段
            segment_count += 1
            output_path = output_dir / f"{input_path.stem}_segment_{segment_count:03d}.wav"
            sf.write(str(output_path), segment_audio, sr)
            
            segments.append(output_path)
            
            start_time = start_sample / sr
            end_time = end_sample / sr
            print(f"✅ 片段 {segment_count}: {start_time:.1f}s - {end_time:.1f}s ({segment_duration:.1f}s) -> {output_path.name}")
            
            # 移动到下一个片段
            start_sample += step_samples
        
        print(f"\n🎉 分割完成！生成了 {len(segments)} 个片段")
        print(f"📁 输出目录: {output_dir}")
        
        return segments
        
    except Exception as e:
        print(f"❌ 分割失败: {str(e)}")
        return []


def find_best_segments(segments, max_segments=5):
    """
    从分割的片段中选择最佳的几个用于 TTS
    
    Args:
        segments: 音频片段路径列表
        max_segments: 最大选择数量
    
    Returns:
        list: 推荐的片段路径列表
    """
    if len(segments) <= max_segments:
        return segments
    
    print(f"\n🎯 从 {len(segments)} 个片段中选择最佳 {max_segments} 个:")
    
    # 简单策略：选择中间部分的片段（通常语音质量更稳定）
    start_idx = len(segments) // 4  # 跳过开头 1/4
    end_idx = len(segments) * 3 // 4  # 到 3/4 位置
    
    if end_idx - start_idx < max_segments:
        # 如果中间部分不够，就均匀选择
        step = len(segments) // max_segments
        selected = segments[::step][:max_segments]
    else:
        # 从中间部分选择
        middle_segments = segments[start_idx:end_idx]
        step = len(middle_segments) // max_segments
        selected = middle_segments[::step][:max_segments]
    
    for i, segment in enumerate(selected, 1):
        print(f"   {i}. {segment.name}")
    
    return selected


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="音频分段工具 - 为 IndexTTS 优化",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 将长音频分割成 10 秒片段
  python split_audio_for_tts.py long_audio.wav
  
  # 自定义片段长度和重叠
  python split_audio_for_tts.py long_audio.wav -l 15 -o 2
  
  # 指定输出目录
  python split_audio_for_tts.py long_audio.wav -d ./segments
  
  # 选择最佳片段
  python split_audio_for_tts.py long_audio.wav --best 3
        """
    )
    
    parser.add_argument('input', help='输入音频文件路径')
    parser.add_argument('-d', '--output-dir', help='输出目录路径')
    parser.add_argument('-l', '--length', type=float, default=10, help='片段长度（秒，默认10）')
    parser.add_argument('-o', '--overlap', type=float, default=1, help='重叠时间（秒，默认1）')
    parser.add_argument('-m', '--min-length', type=float, default=3, help='最小片段长度（秒，默认3）')
    parser.add_argument('--best', type=int, help='选择最佳N个片段')
    
    args = parser.parse_args()
    
    try:
        # 分割音频
        segments = split_audio(
            args.input,
            args.output_dir,
            args.length,
            args.overlap,
            args.min_length
        )
        
        if not segments:
            return 1
        
        # 选择最佳片段
        if args.best and args.best > 0:
            best_segments = find_best_segments(segments, args.best)
            
            # 创建最佳片段目录
            best_dir = Path(segments[0]).parent / "best_segments"
            best_dir.mkdir(exist_ok=True)
            
            print(f"\n📋 复制最佳片段到: {best_dir}")
            for segment in best_segments:
                import shutil
                dest = best_dir / segment.name
                shutil.copy2(segment, dest)
                print(f"   ✅ {dest.name}")
        
        print(f"\n💡 使用建议:")
        print(f"   1. 在 IndexTTS WebUI 中使用这些短片段")
        print(f"   2. 推荐片段长度: 5-15 秒")
        print(f"   3. 选择语音清晰、无背景噪音的片段")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
