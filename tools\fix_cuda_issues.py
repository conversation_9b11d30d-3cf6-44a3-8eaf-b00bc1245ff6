#!/usr/bin/env python3
"""
CUDA 问题诊断和修复工具

这个工具帮助诊断和解决 IndexTTS 中的 CUDA 相关问题
"""

import os
import sys
import subprocess
from pathlib import Path

def check_cuda_environment():
    """检查 CUDA 环境"""
    print("🔍 检查 CUDA 环境")
    print("=" * 40)
    
    # 检查 nvidia-smi
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA 驱动已安装")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version:' in line:
                    cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                    print(f"📊 系统 CUDA 版本: {cuda_version}")
                    break
        else:
            print("❌ NVIDIA 驱动未安装或不可用")
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi 未找到，NVIDIA 驱动可能未安装")
        return False
    
    return True

def check_pytorch_cuda():
    """检查 PyTorch CUDA 支持"""
    print("\n🔍 检查 PyTorch CUDA 支持")
    print("=" * 40)
    
    try:
        import torch
        print(f"✅ PyTorch 版本: {torch.__version__}")
        print(f"📊 PyTorch CUDA 版本: {torch.version.cuda}")
        print(f"🔌 CUDA 可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"🎮 GPU 数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
                
            # 检查显存
            for i in range(torch.cuda.device_count()):
                total_memory = torch.cuda.get_device_properties(i).total_memory
                print(f"   GPU {i} 显存: {total_memory / 1024**3:.1f} GB")
        else:
            print("⚠️  CUDA 不可用")
            
        return torch.cuda.is_available()
        
    except ImportError:
        print("❌ PyTorch 未安装")
        return False
    except Exception as e:
        print(f"❌ 检查 PyTorch 时出错: {e}")
        return False

def test_cuda_operations():
    """测试基本 CUDA 操作"""
    print("\n🧪 测试 CUDA 操作")
    print("=" * 40)
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            print("⚠️  跳过 CUDA 测试（CUDA 不可用）")
            return False
        
        # 测试基本张量操作
        print("🔄 测试基本张量操作...")
        x = torch.randn(100, 100).cuda()
        y = torch.randn(100, 100).cuda()
        z = torch.mm(x, y)
        print("✅ 基本张量操作正常")
        
        # 测试 CUBLAS 操作
        print("🔄 测试 CUBLAS 操作...")
        a = torch.randn(1000, 1000).cuda()
        b = torch.randn(1000, 1000).cuda()
        c = torch.matmul(a, b)
        print("✅ CUBLAS 操作正常")
        
        # 清理显存
        del x, y, z, a, b, c
        torch.cuda.empty_cache()
        
        return True
        
    except RuntimeError as e:
        if "CUBLAS_STATUS_EXECUTION_FAILED" in str(e):
            print("❌ CUBLAS 错误：这是你遇到的问题！")
            print("💡 建议使用 CPU 模式或更新 CUDA 驱动")
        else:
            print(f"❌ CUDA 操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试时出错: {e}")
        return False

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议")
    print("=" * 40)
    
    print("1. 🖥️  使用 CPU 模式（推荐）:")
    print("   uv run python run_webui_cpu.py")
    print("   或")
    print("   uv run python webui.py --cpu")
    
    print("\n2. 🔧 更新 CUDA 驱动:")
    print("   - 访问 NVIDIA 官网下载最新驱动")
    print("   - 确保驱动版本支持 CUDA 12.8+")
    
    print("\n3. 🐍 重新安装 PyTorch:")
    print("   pip uninstall torch torchaudio")
    print("   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121")
    
    print("\n4. 🔋 降低内存使用:")
    print("   uv run python webui.py --fp16")
    
    print("\n5. 🌐 设置环境变量强制 CPU:")
    print("   set CUDA_VISIBLE_DEVICES=")
    print("   uv run python webui.py")

def create_cpu_startup_script():
    """创建 CPU 模式启动脚本"""
    print("\n📝 创建 CPU 模式启动脚本")
    print("=" * 40)
    
    script_content = '''@echo off
echo 启动 IndexTTS WebUI (CPU 模式)
echo ================================
echo 注意：CPU 模式运行速度较慢，但更稳定
echo ================================

set CUDA_VISIBLE_DEVICES=
uv run python webui.py --cpu --verbose

pause
'''
    
    script_path = Path("start_cpu_mode.bat")
    
    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        print(f"✅ 已创建启动脚本: {script_path}")
        print("💡 双击 start_cpu_mode.bat 即可启动 CPU 模式")
    except Exception as e:
        print(f"❌ 创建脚本失败: {e}")

def main():
    """主函数"""
    print("🛠️  IndexTTS CUDA 问题诊断工具")
    print("=" * 50)
    
    # 检查环境
    cuda_available = check_cuda_environment()
    pytorch_cuda = check_pytorch_cuda()
    
    if cuda_available and pytorch_cuda:
        # 测试 CUDA 操作
        cuda_works = test_cuda_operations()
        
        if not cuda_works:
            print("\n❌ CUDA 操作测试失败")
            print("🎯 这解释了为什么 IndexTTS 出现 CUBLAS 错误")
        else:
            print("\n✅ CUDA 环境正常")
            print("🤔 问题可能在其他地方，建议检查模型文件或输入数据")
    
    # 提供解决方案
    suggest_solutions()
    
    # 创建便捷启动脚本
    create_cpu_startup_script()
    
    print("\n🎯 总结:")
    print("- 如果遇到 CUBLAS_STATUS_EXECUTION_FAILED 错误")
    print("- 最简单的解决方案是使用 CPU 模式")
    print("- 运行: uv run python run_webui_cpu.py")

if __name__ == "__main__":
    main()
