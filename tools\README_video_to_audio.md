# 视频转音频工具 - IndexTTS 专用

这个工具集专门为 IndexTTS 项目设计，可以将视频文件转换为项目需要的音频格式。

## 功能特点

- ✅ **专为 IndexTTS 优化**：输出格式完全符合 IndexTTS 要求
- 🎯 **标准音频参数**：22050Hz 采样率，单声道，WAV 格式
- 🚀 **快速转换**：使用 FFmpeg 高效处理
- 📁 **批量处理**：支持整个目录的批量转换
- ⏰ **时间段截取**：可以截取视频的特定时间段
- 🔊 **音量标准化**：自动调整音频音量
- 📊 **质量检查**：检查音频长度和质量

## 工具说明

### 1. `simple_video_to_audio.py` - 简单转换工具 ⭐

**最推荐使用**，使用标准 FFmpeg 命令，兼容性最好。

#### 基本用法

```bash
# 转换单个视频文件
python tools/simple_video_to_audio.py video.mp4

# 指定输出文件名
python tools/simple_video_to_audio.py video.mp4 -o my_audio.wav

# 截取时间段（从30秒开始，持续10秒）
python tools/simple_video_to_audio.py video.mp4 -s 30 -d 10

# 批量转换目录中的所有视频
python tools/simple_video_to_audio.py -b /path/to/videos

# 转换并检查音频属性
python tools/simple_video_to_audio.py video.mp4 --check
```

### 3. `video_to_audio.py` - 完整功能工具

功能更全面的转换工具，支持更多高级选项。

#### 基本用法

```bash
# 转换单个文件
python tools/video_to_audio.py input.mp4

# 批量转换（递归搜索子目录）
python tools/video_to_audio.py -b /path/to/videos -r

# 高级选项
python tools/video_to_audio.py input.mp4 \
    --sample-rate 22050 \
    --remove-silence \
    --min-duration 1.0 \
    --max-duration 60.0
```

## 支持的格式

### 输入格式（视频）
- MP4, AVI, MKV, MOV, WMV, FLV, WebM
- M4V, 3GP, OGV, TS, MTS, M2TS

### 输入格式（音频）
- WAV, MP3, FLAC, AAC, OGG, M4A, WMA

### 输出格式
- WAV (16-bit PCM, 22050Hz, 单声道)

## 安装依赖

工具使用的依赖库在 IndexTTS 项目中已经包含，如果单独使用需要安装：

```bash
# 基础依赖
pip install ffmpeg-python librosa soundfile numpy tqdm

# 或者在 IndexTTS 环境中直接使用
uv run python tools/quick_video_to_audio.py video.mp4
```

## 使用示例

### 示例1：转换单个视频文件

```bash
# 将 video.mp4 转换为 IndexTTS 可用的音频
python tools/quick_video_to_audio.py video.mp4

# 输出文件：video_for_indextts.wav
```

### 示例2：截取视频片段

```bash
# 从视频的第30秒开始，截取10秒的音频
python tools/quick_video_to_audio.py video.mp4 -s 30 -d 10 -o clip.wav
```

### 示例3：批量转换

```bash
# 转换 videos 目录中的所有视频文件
python tools/quick_video_to_audio.py -b ./videos

# 输出目录：./videos/audio_for_indextts/
```

### 示例4：在 IndexTTS 中使用转换后的音频

```python
from indextts.infer_v2 import IndexTTS2

# 初始化 IndexTTS
tts = IndexTTS2(
    cfg_path="checkpoints/config.yaml", 
    model_dir="checkpoints"
)

# 使用转换后的音频作为语音提示
tts.infer(
    spk_audio_prompt='video_for_indextts.wav',  # 使用转换后的音频
    text="你好，这是使用视频提取的音频进行语音合成的测试。",
    output_path="generated_speech.wav"
)
```

## 输出音频规格

转换后的音频文件具有以下规格，完全符合 IndexTTS 要求：

- **采样率**：22050 Hz
- **声道数**：1（单声道）
- **位深度**：16-bit
- **格式**：WAV (PCM)
- **音频范围**：[-1, 1]
- **音量**：已标准化

## 常见问题

### Q: 转换后的音频文件很大怎么办？
A: 这是正常的，WAV 格式是无损的。如果需要压缩，可以考虑转换为 MP3，但建议在 IndexTTS 中使用 WAV 格式以获得最佳质量。

### Q: 支持哪些视频格式？
A: 支持大部分常见格式，包括 MP4、AVI、MKV、MOV 等。如果遇到不支持的格式，可以先用其他工具转换为 MP4。

### Q: 如何处理很长的视频？
A: 可以使用 `-s` 和 `-d` 参数截取需要的片段，或者使用 `--max-duration` 参数限制最大长度。

### Q: 音频质量不好怎么办？
A: 确保原视频的音频质量良好。工具会自动进行音量标准化，如果需要更多处理，可以使用完整版工具的高级选项。

## 技术说明

### 音频处理流程

1. **提取音频**：使用 FFmpeg 从视频中提取音频轨道
2. **格式转换**：转换为 22050Hz 单声道 WAV 格式
3. **音量标准化**：使用 loudnorm 滤镜标准化音量
4. **质量检查**：检查音频长度和数值范围
5. **后处理**：确保音频值在 [-1, 1] 范围内

### 性能优化

- 使用 FFmpeg 的硬件加速（如果可用）
- 批量处理时显示进度条
- 自动跳过已存在的文件（可配置）

## 更新日志

- **v1.0.0**：初始版本，支持基本的视频转音频功能
- 支持单文件和批量转换
- 支持时间段截取
- 音频质量优化
