# IndexTTS 内存优化指南

## 🚨 问题诊断

你遇到的错误表明 IndexTTS 在处理长音频时需要大量内存：

```
RuntimeError: [enforce fail at alloc_cpu.cpp:121] data. DefaultCPUAllocator: not enough memory: you tried to allocate 18335326464 bytes.
```

这是因为 wav2vec2_bert 模型对长音频序列的内存需求呈平方级增长。

## ✅ 解决方案

### 1. 使用分割后的短音频片段 ⭐

**最重要的解决方案**：使用我们刚才生成的短音频片段。

#### 推荐使用的音频片段：
```
D:\video\20210725_Tomy聊聊说-感悟_for_indextts_segments\best_segments\
├── 20210725_Tomy聊聊说-感悟_for_indextts_segment_005.wav  (36-46秒)
├── 20210725_Tomy聊聊说-感悟_for_indextts_segment_007.wav  (54-64秒)
├── 20210725_Tomy聊聊说-感悟_for_indextts_segment_009.wav  (72-82秒)
├── 20210725_Tomy聊聊说-感悟_for_indextts_segment_011.wav  (90-100秒)
└── 20210725_Tomy聊聊说-感悟_for_indextts_segment_013.wav  (108-118秒)
```

### 2. 启动内存优化模式

```bash
# 使用内存优化启动脚本
uv run python run_webui_memory_optimized.py
```

### 3. 音频长度建议

| 音频长度 | 内存需求 | 推荐程度 | 说明 |
|---------|---------|---------|------|
| 3-5秒   | 很低    | ⭐⭐⭐⭐⭐ | 最佳选择，快速稳定 |
| 5-10秒  | 低      | ⭐⭐⭐⭐   | 推荐使用 |
| 10-15秒 | 中等    | ⭐⭐⭐     | 可以尝试 |
| 15-30秒 | 高      | ⭐⭐       | 可能出现内存问题 |
| >30秒   | 很高    | ❌        | 不推荐，容易崩溃 |

## 🎯 使用步骤

### 步骤1：启动内存优化的 WebUI

```bash
uv run python run_webui_memory_optimized.py
```

### 步骤2：在浏览器中打开 WebUI

访问：`http://localhost:7860`

### 步骤3：使用短音频片段

1. **上传音频**：选择 `best_segments` 目录中的任一文件
2. **输入文本**：输入你想要合成的文本
3. **开始生成**：点击生成按钮

### 步骤4：测试不同片段

尝试不同的音频片段，找到效果最好的：

- `segment_005.wav` - 来自原音频 36-46 秒
- `segment_007.wav` - 来自原音频 54-64 秒  
- `segment_009.wav` - 来自原音频 72-82 秒
- `segment_011.wav` - 来自原音频 90-100 秒
- `segment_013.wav` - 来自原音频 108-118 秒

## 🔧 故障排除

### 如果仍然内存不足：

1. **使用更短的片段**：
   ```bash
   uv run python tools/split_audio_for_tts.py "D:\video\20210725_Tomy聊聊说-感悟_for_indextts.wav" -l 5 -o 0.5 --best 3
   ```

2. **重启程序**：
   - 关闭 WebUI (Ctrl+C)
   - 重新启动内存优化模式

3. **检查系统内存**：
   ```bash
   # Windows
   wmic OS get TotalVisibleMemorySize,FreePhysicalMemory
   ```

4. **关闭其他程序**：
   - 关闭浏览器多余标签页
   - 关闭不必要的应用程序

### 如果 CPU 模式太慢：

1. **尝试修复 CUDA 问题**：
   ```bash
   # 更新 NVIDIA 驱动
   # 或重新安装匹配的 PyTorch
   pip uninstall torch torchaudio
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121
   ```

2. **使用 FP16 模式**（如果 CUDA 可用）：
   ```bash
   uv run python webui.py --fp16
   ```

## 📊 性能对比

| 模式 | 音频长度 | 处理时间 | 内存使用 | 稳定性 |
|------|---------|---------|---------|--------|
| GPU (原始) | 169秒 | ❌ 崩溃 | ❌ 崩溃 | ❌ |
| CPU (原始) | 169秒 | ❌ 内存不足 | ❌ 18GB | ❌ |
| CPU (10秒片段) | 10秒 | ~30-60秒 | ~2-4GB | ✅ |
| CPU (5秒片段) | 5秒 | ~15-30秒 | ~1-2GB | ✅⭐ |

## 💡 最佳实践

### 1. 音频选择
- 选择语音清晰的片段
- 避免有背景音乐或噪音的部分
- 优先选择情感表达丰富的片段

### 2. 文本输入
- 使用简短的句子测试
- 避免过长的文本（建议 <50 字）
- 使用标点符号帮助语调控制

### 3. 系统优化
- 确保有足够的系统内存（推荐 8GB+）
- 关闭不必要的后台程序
- 定期重启 WebUI 清理内存

## 🎉 成功示例

使用 10 秒音频片段的典型工作流程：

1. **启动**：`uv run python run_webui_memory_optimized.py`
2. **上传**：`segment_007.wav` (54-64秒片段)
3. **输入**：`"你好，这是一个语音合成测试。"`
4. **生成**：等待 30-60 秒
5. **结果**：获得高质量的合成语音

## 📞 获取帮助

如果问题仍然存在：

1. **检查音频片段**：
   ```bash
   uv run python -c "import librosa; audio, sr = librosa.load('your_segment.wav', sr=None); print(f'时长: {len(audio)/sr:.2f}秒')"
   ```

2. **监控内存使用**：
   - 任务管理器 (Windows)
   - Activity Monitor (macOS)
   - htop (Linux)

3. **查看详细错误**：
   - 启动时使用 `--verbose` 参数
   - 检查控制台输出

记住：**短音频片段是成功的关键！** 🎯
