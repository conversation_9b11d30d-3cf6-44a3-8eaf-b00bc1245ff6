{"本软件以自拟协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "本软件以自拟协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.", "时长必须为正数": "时长必须为正数", "请输入有效的浮点数": "请输入有效的浮点数", "使用情感参考音频": "使用情感参考音频", "使用情感向量控制": "使用情感向量控制", "使用情感描述文本控制": "使用情感描述文本控制", "上传情感参考音频": "上传情感参考音频", "情感权重": "情感权重", "喜": "喜", "怒": "怒", "哀": "哀", "惧": "惧", "厌恶": "厌恶", "低落": "低落", "惊喜": "惊喜", "平静": "平静", "情感描述文本": "情感描述文本", "请输入情绪描述（或留空以自动使用目标文本作为情绪描述）": "请输入情绪描述（或留空以自动使用目标文本作为情绪描述）", "高级生成参数设置": "高级生成参数设置", "情感向量之和不能超过1.5，请调整后重试。": "情感向量之和不能超过1.5，请调整后重试。", "音色参考音频": "音色参考音频", "音频生成": "音频生成", "文本": "文本", "生成语音": "生成语音", "生成结果": "生成结果", "功能设置": "功能设置", "分句设置": "分句设置", "参数会影响音频质量和生成速度": "参数会影响音频质量和生成速度", "分句最大Token数": "分句最大Token数", "建议80~200之间，值越大，分句越长；值越小，分句越碎；过小过大都可能导致音频质量不高": "建议80~200之间，值越大，分句越长；值越小，分句越碎；过小过大都可能导致音频质量不高", "预览分句结果": "预览分句结果", "序号": "序号", "分句内容": "分句内容", "Token数": "Token数", "情感控制方式": "情感控制方式", "GPT2 采样设置": "GPT2 采样设置", "参数会影响音频多样性和生成速度详见": "参数会影响音频多样性和生成速度详见", "是否进行采样": "是否进行采样", "生成Token最大数量，过小导致音频被截断": "生成Token最大数量，过小导致音频被截断"}