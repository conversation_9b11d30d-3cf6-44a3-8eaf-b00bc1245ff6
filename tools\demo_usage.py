#!/usr/bin/env python3
"""
视频转音频工具使用演示

这个脚本展示了如何使用视频转音频工具，并与 IndexTTS 集成
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tools.quick_video_to_audio import convert_video_to_audio


def demo_single_conversion():
    """演示单个文件转换"""
    print("=" * 60)
    print("🎬 演示：单个视频文件转换")
    print("=" * 60)
    
    # 检查是否有示例视频文件
    example_videos = [
        "example.mp4",
        "test.avi",
        "sample.mov",
        "demo.mkv"
    ]
    
    video_file = None
    for video in example_videos:
        if Path(video).exists():
            video_file = video
            break
    
    if not video_file:
        print("❌ 未找到示例视频文件")
        print("💡 请将视频文件放在当前目录，支持的格式：")
        print("   MP4, AVI, MKV, MOV, WMV, FLV, WebM 等")
        return False
    
    print(f"📁 找到视频文件: {video_file}")
    
    # 转换视频
    output_file = f"{Path(video_file).stem}_for_indextts.wav"
    
    if convert_video_to_audio(video_file, output_file):
        print(f"✅ 转换成功！输出文件: {output_file}")
        
        # 显示如何在 IndexTTS 中使用
        print("\n🎯 在 IndexTTS 中使用转换后的音频：")
        print(f"""
from indextts.infer_v2 import IndexTTS2

# 初始化 IndexTTS
tts = IndexTTS2(
    cfg_path="checkpoints/config.yaml", 
    model_dir="checkpoints"
)

# 使用转换后的音频进行语音合成
tts.infer(
    spk_audio_prompt='{output_file}',
    text="这是使用视频提取音频进行语音合成的测试。",
    output_path="generated_speech.wav"
)
        """)
        return True
    else:
        print("❌ 转换失败")
        return False


def demo_batch_conversion():
    """演示批量转换"""
    print("=" * 60)
    print("📁 演示：批量视频转换")
    print("=" * 60)
    
    # 创建示例目录结构
    demo_dir = Path("demo_videos")
    if not demo_dir.exists():
        print(f"❌ 演示目录不存在: {demo_dir}")
        print("💡 请创建 'demo_videos' 目录并放入一些视频文件")
        return False
    
    # 查找视频文件
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(demo_dir.glob(f"*{ext}"))
        video_files.extend(demo_dir.glob(f"*{ext.upper()}"))
    
    if not video_files:
        print(f"❌ 在 {demo_dir} 中未找到视频文件")
        return False
    
    print(f"📂 找到 {len(video_files)} 个视频文件:")
    for video in video_files:
        print(f"   - {video.name}")
    
    # 批量转换
    from tools.quick_video_to_audio import batch_convert
    
    output_dir = demo_dir / "audio_output"
    success, total = batch_convert(demo_dir, output_dir)
    
    if success > 0:
        print(f"\n✅ 批量转换完成: {success}/{total} 个文件成功")
        print(f"📁 输出目录: {output_dir}")
        
        # 列出转换后的音频文件
        audio_files = list(output_dir.glob("*.wav"))
        if audio_files:
            print("\n🎵 转换后的音频文件:")
            for audio in audio_files:
                size_mb = audio.stat().st_size / (1024 * 1024)
                print(f"   - {audio.name} ({size_mb:.2f} MB)")
        
        return True
    else:
        print("❌ 批量转换失败")
        return False


def demo_time_segment():
    """演示时间段截取"""
    print("=" * 60)
    print("⏰ 演示：时间段截取")
    print("=" * 60)
    
    # 查找一个视频文件
    video_files = []
    for ext in ['.mp4', '.avi', '.mkv', '.mov']:
        video_files.extend(Path('.').glob(f"*{ext}"))
    
    if not video_files:
        print("❌ 未找到视频文件进行演示")
        return False
    
    video_file = video_files[0]
    print(f"📁 使用视频文件: {video_file}")
    
    # 截取前10秒
    output_file = f"{video_file.stem}_first_10s.wav"
    
    print("⏱️  截取前10秒音频...")
    if convert_video_to_audio(str(video_file), output_file, start_time=0, duration=10):
        print(f"✅ 截取成功: {output_file}")
        
        # 截取中间10秒（从30秒开始）
        output_file2 = f"{video_file.stem}_middle_10s.wav"
        print("⏱️  截取中间10秒音频（从30秒开始）...")
        
        if convert_video_to_audio(str(video_file), output_file2, start_time=30, duration=10):
            print(f"✅ 截取成功: {output_file2}")
            return True
    
    print("❌ 时间段截取失败")
    return False


def demo_integration_with_indextts():
    """演示与 IndexTTS 的集成"""
    print("=" * 60)
    print("🎯 演示：与 IndexTTS 集成")
    print("=" * 60)
    
    # 查找转换后的音频文件
    audio_files = list(Path('.').glob("*_for_indextts.wav"))
    
    if not audio_files:
        print("❌ 未找到转换后的音频文件")
        print("💡 请先运行单个文件转换演示")
        return False
    
    audio_file = audio_files[0]
    print(f"🎵 使用音频文件: {audio_file}")
    
    try:
        # 尝试导入和使用 IndexTTS
        from indextts.infer_v2 import IndexTTS2
        
        print("🚀 初始化 IndexTTS...")
        tts = IndexTTS2(
            cfg_path="checkpoints/config.yaml",
            model_dir="checkpoints",
            use_fp16=False,
            use_cuda_kernel=False,
            use_deepspeed=False
        )
        
        # 进行语音合成
        test_text = "这是使用从视频中提取的音频进行语音合成的测试。效果如何呢？"
        output_path = "demo_generated_speech.wav"
        
        print("🎤 开始语音合成...")
        print(f"📝 文本: {test_text}")
        
        tts.infer(
            spk_audio_prompt=str(audio_file),
            text=test_text,
            output_path=output_path,
            use_random=False,
            verbose=True
        )
        
        if Path(output_path).exists():
            print(f"✅ 语音合成成功: {output_path}")
            size_mb = Path(output_path).stat().st_size / (1024 * 1024)
            print(f"📁 文件大小: {size_mb:.2f} MB")
            return True
        else:
            print("❌ 语音合成失败")
            return False
            
    except ImportError:
        print("❌ 无法导入 IndexTTS，请确保在正确的环境中运行")
        return False
    except Exception as e:
        print(f"❌ IndexTTS 集成失败: {str(e)}")
        return False


def main():
    """主演示函数"""
    print("🎬 视频转音频工具演示")
    print("=" * 60)
    print("这个演示将展示如何使用视频转音频工具，并与 IndexTTS 集成")
    print()
    
    demos = [
        ("1️⃣  单个文件转换", demo_single_conversion),
        ("2️⃣  批量文件转换", demo_batch_conversion),
        ("3️⃣  时间段截取", demo_time_segment),
        ("4️⃣  IndexTTS 集成", demo_integration_with_indextts),
    ]
    
    while True:
        print("\n请选择演示项目:")
        for i, (name, _) in enumerate(demos):
            print(f"  {name}")
        print("  0️⃣  退出")
        
        try:
            choice = input("\n请输入选择 (0-4): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice in ['1', '2', '3', '4']:
                demo_index = int(choice) - 1
                name, demo_func = demos[demo_index]
                print(f"\n🚀 开始演示: {name}")
                demo_func()
            else:
                print("❌ 无效选择，请输入 0-4")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 演示出错: {str(e)}")


if __name__ == "__main__":
    main()
