{"本软件以自拟协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "This software is open-sourced under customized license. The author has no control over the software, and users of the software, as well as those who distribute the audio generated by the software, assume full responsibility.", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "If you do not agree to these terms, you are not permitted to use or reference any code or files within the software package. For further details, please refer to the LICENSE files in the root directory.", "时长必须为正数": "Duration must be a positive number", "请输入有效的浮点数": "Please enter a valid floating-point number", "使用情感参考音频": "Use emotion reference audio", "使用情感向量控制": "Use emotion vectors", "使用情感描述文本控制": "Use text description to control emotion", "上传情感参考音频": "Upload emotion reference audio", "情感权重": "Emotion control weight", "喜": "Happy", "怒": "Angry", "哀": "Sad", "惧": "<PERSON><PERSON><PERSON>", "厌恶": "Disgusted", "低落": "Melancholic", "惊喜": "Surprised", "平静": "Calm", "情感描述文本": "Emotion description", "请输入情绪描述（或留空以自动使用目标文本作为情绪描述）": "Please input an emotion description (or leave blank to automatically use the main text prompt)", "高级生成参数设置": "Advanced generation parameter settings", "情感向量之和不能超过1.5，请调整后重试。": "The sum of the emotion vectors cannot exceed 1.5. Please adjust and try again.", "音色参考音频": "Voice Reference", "音频生成": "Speech Synthesis", "文本": "Text", "生成语音": "Synthesize", "生成结果": "Synthesis Result", "功能设置": "Settings", "分句设置": "Text segmentation settings", "参数会影响音频质量和生成速度": "These parameters affect the audio quality and generation speed.", "分句最大Token数": "Max tokens per generation segment", "建议80~200之间，值越大，分句越长；值越小，分句越碎；过小过大都可能导致音频质量不高": "Recommended range: 80 - 200. Larger values require more VRAM but improves the flow of the speech, while lower values require less VRAM but means more fragmented sentences. Values that are too small or too large may lead to less coherent speech.", "预览分句结果": "Preview of the audio generation segments", "序号": "Index", "分句内容": "Content", "Token数": "Token Count", "情感控制方式": "Emotion control method", "GPT2 采样设置": "GPT-2 Sampling Configuration", "参数会影响音频多样性和生成速度详见": "Influences both the diversity of the generated audio and the generation speed. For further details, refer to", "是否进行采样": "Enable GPT-2 sampling", "生成Token最大数量，过小导致音频被截断": "Maximum number of tokens to generate. If text exceeds this, the audio will be cut off.", "请上传情感参考音频": "Please upload the emotion reference audio", "当前模型版本": "Current model version: ", "请输入目标文本": "Please input the text to synthesize", "例如：高兴，愤怒，悲伤等": "e.g., happy, angry, sad, etc.", "与音色参考音频相同": "Same as the voice reference", "情感随机采样": "Randomize emotion sampling"}